import { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tag,
  Link,
  Drawer,
  Descriptions,
  Divider,
  Tabs,
  Typography,
  Timeline,
  Modal,
  Badge,
  Form,
} from '@arco-design/web-react';
import {
  IconSearch,
  IconPlus,
  IconCalendar,
  IconUser,
  IconUnorderedList,
} from '@arco-design/web-react/icon';
import request, { baseUrl } from '@/utils/request';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import TimelineItem from '@arco-design/web-react/es/Timeline/item';
import FormItem from '@arco-design/web-react/es/Form/form-item';

const { RangePicker } = DatePicker;

type DynamicObject = {
  [key: string]: any;
};

const getInvoiceTypeTag = (type) => {
  const typeMap = {
    normal: { text: '普通发票', color: 'gray' },
    special: { text: '增值税专票', color: 'orangered' },
  };
  const config = typeMap[type] || { text: type, color: 'gray' };
  return <Tag color={config.color}>{config.text}</Tag>;
}

const getInvoiceStateTag = (state) => {
  const stateMap = {
    success: { text: '已开票', color: 'green' },
    cancelled: { text: '已作废', color: 'red' },
    new: { text: '待开票', color: 'gold' },
    fail: { text: '失败', color: 'red' },
  };
  const config = stateMap[state] || { text: state, color: 'gray' };
  return <Tag color={config.color}>{config.text}</Tag>;
}

const getInvoiceKindBadge = (kind) => {
  const kindMap = {
    blue: { text: '蓝票', color: 'processing' },
    red: { text: '红票', color: 'error' },
  };
  const config = kindMap[kind] || { text: kind, color: 'gray' };
  return <Badge status={config.color} text={config.text} />;
}

export default function InvoiceList() {
  const [data, setData] = useState([]);
  const [keyword, setKeyword] = useState('');
  const [state, setState] = useState('');
  const [dateRange, setDateRange] = useState([]);
  const [infoVisible, setInfoVisible] = useState(false);
  const [rowSelection, setRowSelection] = useState<DynamicObject>({});
  const [previewVisible, setPreviewVisible] = useState(false);
  const [reopenVisible, setReopenVisible] = useState(false);
  const [form] = Form.useForm();
  const [pdfUrl, setPdfUrl] = useState({
    previewUrl: '',
    downloadUrl: ''
  });
  const [pagination, setPagination] = useState({
    total: 0,
    pageSize: 10,
    current: 1,
  });

  const fetchData = () => {
    request
      .get('/order/list', { keyword, state, start_date: dateRange[0], end_date: dateRange[1], ...pagination })
      .then((res) => {
        setData(res.data.data);
        setPagination({
          ...pagination,
          total: res.data.total,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const columns = [
    {
      title: '订单信息',
      dataIndex: 'order_no',
      key: 'order_no',
      width: 180,
      render: (_, record) => {
        return (
          <Space direction="vertical">
            <div>{record.order_no}</div>
            <div style={{ fontSize: 12, color: '#999' }}>{record.name} {record.phone}</div>
          </Space>
        )
      }
    },
    {
      title: '金额',
      dataIndex: 'formatted_total_amount',
      key: 'formatted_total_amount',
      width: 120,
      render: (value) => `¥${value}`,
    },
    {
      title: '发票类型',
      dataIndex: 'invoice',
      key: 'invoice_type',
      width: 90,
      render: (invoice) => {
        return invoice ? getInvoiceTypeTag(invoice.invoice_type) : '-';
      },
    },
    {
      title: '发票抬头',
      dataIndex: 'invoice',
      key: 'invoice_title',
      render: (invoice) => {
        return invoice ? invoice.title : '-';
      },
    },
    {
      title: '发票号码',
      dataIndex: 'invoice',
      key: 'invoice_no',
      render: (invoice) => {
        return invoice ? invoice.invoice_no : '-';
      },
    },
    {
      title: '开票日期',
      dataIndex: 'invoice',
      key: 'invoice_date',
      width: 180,
      render: (invoice) => {
        return invoice ? invoice.invoice_date : '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (state, record) => {
        if (record?.invoice?.type === 'red') {
          state = 'cancelled';
        }
        return getInvoiceStateTag(state);
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 170,
      render: (_, record) => (
        <Space key="action">
          {record.state === 'success' && <Link
            key="view"
            href="#"
            disabled={!record?.invoice?.invoice_no || !record?.invoice?.invoice_url?.pdfUrl}
            onClick={() => {
              setPreviewVisible(true);
              setRowSelection(record);
              setPdfUrl({
                previewUrl: `${baseUrl}/file/invoice/${record.invoice.invoice_no}.pdf`,
                downloadUrl: record.invoice.invoice_url.pdfUrl,
              });
            }}
          >
            查看
          </Link>
          }
          <Link
            key="info"
            href="#"
            onClick={() => {
              setInfoVisible(true);
              setRowSelection(record);
            }}
          >
            详情
          </Link>
          {(record?.state === 'success' && record?.invoice?.type === 'red') && <Link
            key="reopen"
            href='#'
            onClick={() => {
              setReopenVisible(true);
              setRowSelection(record);
            }}
          >
            换开
          </Link>}

        </Space>
      ),
    },
  ];

  const handleDirectPrint = (url) => {
    const existingIframe = document.getElementById('print-iframe');
    if (existingIframe) {
      // 如果已存在，则重用它
      existingIframe.src = url;
      existingIframe.onload = () => {
        try {
          existingIframe.contentWindow.print();
        } catch (error) {
          console.error('打印失败:', error);
          window.open(url, '_blank');
        }
      };
      return;
    }

    // 创建一个隐藏的 iframe 用于打印
    const iframe = document.createElement('iframe');
    iframe.id = 'print-iframe'; // 添加 ID 以便后续引用
    iframe.style.position = 'fixed';
    iframe.style.right = '-9999px'; // 放在屏幕外而不是 display:none，避免某些浏览器的打印问题
    iframe.style.bottom = '0';
    iframe.style.width = '0';
    iframe.style.height = '0';
    iframe.style.border = '0';
    iframe.src = url;

    document.body.appendChild(iframe);

    iframe.onload = () => {
      try {
        iframe.contentWindow.print();
      } catch (error) {
        console.error('打印失败:', error);
        window.open(url, '_blank');
      }
    };
  };

  useEffect(() => {
    fetchData();
    return () => {
      const printIframe = document.getElementById('print-iframe');
      if (printIframe) {
        document.body.removeChild(printIframe);
      }
    };
  }, [pagination.current]);

  const handleModalClose = () => {
    setPreviewVisible(false);
    const printIframe = document.getElementById('print-iframe');
    if (printIframe) {
      document.body.removeChild(printIframe);
    }
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="请输入单号或姓名手机"
              prefix={<IconSearch />}
              onChange={(value) => setKeyword(value)}
              onPressEnter={() => {
                fetchData();
              }}
              allowClear
              style={{ width: 200 }}
            />
            <Select
              placeholder="请选择状态"
              style={{ width: 120 }}
              allowClear
              onChange={(value) => setState(value)}
              options={[
                { label: '已开票', value: 'success' },
                { label: '已作废', value: 'cancelled' },
                { label: '待处理', value: 'new' },
              ]}

            />
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={(value) => setDateRange(value)}
            />
            <Button type="primary" icon={<IconSearch />} onClick={() => {
              fetchData();
            }}>
              查询
            </Button>
            <Button icon={<IconPlus />}>新增发票</Button>
          </Space>
        </div>
        <Table
          rowKey="id"
          columns={columns}
          data={data}
          pagination={pagination}
        />
      </Card>
      {/* 详情抽屉 */}
      <Drawer
        width="50%"
        title="详情"
        visible={infoVisible}
        onOk={() => {
          setInfoVisible(false);
        }}
        onCancel={() => {
          setInfoVisible(false);
        }}
        footer={null}
      >
        <Descriptions
          colon=""
          title="单据详情"
          column={2}
          labelStyle={{ width: 60 }}
          data={[
            {
              label: '姓名',
              value: rowSelection?.name,
            },
            {
              label: '手机号',
              value: rowSelection?.phone,
            },
            {
              label: '单号',
              value: rowSelection?.order_no,
            },
            {
              label: '金额',
              value: `￥${rowSelection?.formatted_total_amount}`,
            },
            {
              label: '时间',
              value: rowSelection?.created_at,
            },
            {
              label: '状态',
              value: getInvoiceStateTag(rowSelection?.state),
            },
          ]}
        />
        <Divider />
        <Tabs defaultActiveTab="1">
          <TabPane
            key="1"
            title={
              <span>
                <IconCalendar style={{ marginRight: 6 }} />
                发票明细
              </span>
            }
          >
            <Typography.Paragraph>
              <Table
                rowKey="id"
                columns={[
                  {
                    title: '发票种类',
                    dataIndex: 'type',
                    key: 'type',
                    render: (type) => {
                      return getInvoiceKindBadge(type);
                    },
                  },
                  {
                    title: '发票类型',
                    dataIndex: 'invoice_type',
                    key: 'invoice_type',
                    render: (invoice_type) => {
                      return getInvoiceTypeTag(invoice_type);
                    },
                  },
                  {
                    title: '发票号码',
                    dataIndex: 'invoice_no',
                    key: 'invoice_no',
                  },
                  {
                    title: '状态',
                    dataIndex: 'state',
                    key: 'state',
                    render: (state) => {
                      return getInvoiceStateTag(state);
                    },
                  },
                  {
                    title: '操作',
                    key: 'action',
                    width: 120,
                    render: (_, record: { invoice_no: string, invoice_url: { pdfUrl: string } }) => (
                      <Space>
                        <Link href="#" disabled={!record?.invoice_url?.pdfUrl || !record?.invoice_no} onClick={() => {
                          setPdfUrl({
                            previewUrl: `${baseUrl}/file/invoice/${record.invoice_no}.pdf`,
                            downloadUrl: record.invoice_url.pdfUrl,
                          });
                          setPreviewVisible(true);
                        }}>查看</Link>
                        <Link href={record?.invoice_url?.pdfUrl} disabled={!record?.invoice_url?.pdfUrl}>下载</Link>
                      </Space>
                    ),
                  },
                ]}
                data={rowSelection?.invoices}
                pagination={false}
                size="small"
              />
            </Typography.Paragraph>
          </TabPane>
          <TabPane
            key="2"
            title={
              <span>
                <IconUnorderedList style={{ marginRight: 6 }} />
                订单明细
              </span>
            }
          >
            <Typography.Paragraph>
              <Table
                rowKey="id"
                columns={[
                  {
                    title: '商品名称',
                    dataIndex: 'article_name',
                    key: 'article_name',
                  },
                  {
                    title: '单位',
                    dataIndex: 'unit',
                    key: 'unit',
                  },
                  {
                    title: '数量',
                    dataIndex: 'num',
                    key: 'num',
                  },
                  {
                    title: '金额',
                    dataIndex: 'amount',
                    key: 'amount',
                  },
                ]}
                data={rowSelection?.detail}
                pagination={false}
                size="small"
                stripe
              />
            </Typography.Paragraph>
          </TabPane>
          <TabPane
            key="3"
            title={
              <span>
                <IconUser style={{ marginRight: 6 }} />
                操作记录
              </span>
            }
          >
            <Typography.Paragraph>
              <Timeline mode='left' labelPosition='relative'>
                {rowSelection?.logs?.map(item => {
                  return (
                    <TimelineItem key={item.id} label={item.formatted_created_at}>
                      <div style={{ marginBottom: 12 }}>
                        {item.info}
                        <div style={{ fontSize: 12, color: '#4E5969' }}>操作人：{item.operator_id == 0 ? '系统' : item?.operator?.name}</div>
                      </div>
                    </TimelineItem>
                  );
                })}
              </Timeline>
            </Typography.Paragraph>
          </TabPane>
        </Tabs>
      </Drawer>
      {/* 查看发票 */}
      <Modal
        key="preview"
        title="发票预览"
        visible={previewVisible}
        unmountOnExit
        style={{ width: '860px', maxWidth: '1400px' }}
        footer={
          <>
            <Button
              onClick={() => {
                window.location.href = pdfUrl.downloadUrl;
              }}
            >
              下载
            </Button>
            <Button
              onClick={() => {
                handleDirectPrint(pdfUrl.previewUrl);
              }}
              type="primary"
            >
              打印
            </Button>
          </>
        }
        onCancel={handleModalClose}
      >
        <object
          data={pdfUrl.previewUrl + '#toolbar=0'}
          type="application/pdf"
          width="100%"
          height="540px"
          style={{ border: 'none' }}
        >
          <p>
            PDF 加载失败，请
            <a href={pdfUrl.downloadUrl}>下载 PDF</a>
            查看。
          </p>
        </object>
      </Modal>
      {/* 重开发票 */}
      <Modal
        key="reopen"
        title="填写开票信息"
        visible={reopenVisible}
        unmountOnExit
        style={{ width: '860px', maxWidth: '1400px' }}
        onCancel={() => setReopenVisible(false)}
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="发票类型"
            field="invoice_type"
            rules={[{ required: true, message: '请选择发票类型' }]}
          >
            <Select
              placeholder="请选择发票类型"
              options={[
                { label: '普通发票', value: 'normal' },
                { label: '增值税专票', value: 'special' },
              ]}
            />
          </FormItem>
          
          <FormItem
            label="备注"
            field="remark"
          >
            <Input.TextArea placeholder="请输入备注" />
          </FormItem>
        </Form>
      </Modal>
    </div>
  );
}
