import{j as e,f as o,aA as s,w as l,aB as m,aC as k,a as c,ai as p,T as h,aj as f,D as y,M as v}from"./vendor.1faad458.js";/* empty css               *//* empty css               */import{u as g}from"./index.9ffb96ac.js";import{i as _}from"./index.9464998a.js";const w="_shortcuts_f9b1x_1",x="_item_f9b1x_5",M="_icon_f9b1x_14",u="_title_f9b1x_20",j="_recent_f9b1x_41";var i={shortcuts:w,item:x,icon:M,title:u,recent:j};function A(){const t=g(_),r=[{title:t["workplace.contentMgmt"],key:"Content Management",icon:e(o,{})},{title:t["workplace.contentStatistic"],key:"Content Statistic",icon:e(s,{})},{title:t["workplace.advancedMgmt"],key:"Advanced Management",icon:e(l,{})},{title:t["workplace.onlinePromotion"],key:"Online Promotion",icon:e(m,{})},{title:t["workplace.marketing"],key:"Marketing",icon:e(k,{})}],d=[{title:t["workplace.contentStatistic"],key:"Content Statistic",icon:e(s,{})},{title:t["workplace.contentMgmt"],key:"Content Management",icon:e(o,{})},{title:t["workplace.advancedMgmt"],key:"Advanced Management",icon:e(l,{})}];function a(n){v.info({content:c("span",{children:["You clicked ",e("b",{children:n})]})})}return c(p,{children:[c("div",{style:{display:"flex",justifyContent:"space-between"},children:[e(h.Title,{heading:6,children:t["workplace.shortcuts"]}),e(f,{children:t["workplace.seeMore"]})]}),e("div",{className:i.shortcuts,children:r.map(n=>c("div",{className:i.item,onClick:()=>a(n.key),children:[e("div",{className:i.icon,children:n.icon}),e("div",{className:i.title,children:n.title})]},n.key))}),e(y,{}),e("div",{className:i.recent,children:t["workplace.recent"]}),e("div",{className:i.shortcuts,children:d.map(n=>c("div",{className:i.item,onClick:()=>a(n.key),children:[e("div",{className:i.icon,children:n.icon}),e("div",{className:i.title,children:n.title})]},n.key))})]})}export{A as default};
