import{r as t,a as f,ai as m,j as o,T as x,i as g,l as h}from"./vendor.1faad458.js";/* empty css               */import{D as y}from"./index.6dde18eb.js";import{u as b}from"./index.9ffb96ac.js";import{i as F}from"./index.9464998a.js";function D(){const r=b(F),[s,i]=t.exports.useState([]),[n,a]=t.exports.useState(!0),l=()=>{a(!0),h.get("/api/workplace/content-percentage").then(e=>{i(e.data)}).finally(()=>{a(!1)})};return t.exports.useEffect(()=>{l()},[]),f(m,{children:[o(x.Title,{heading:6,children:r["workplace.contentPercentage"]}),o(g,{loading:n,style:{display:"block"},children:o(y,{autoFit:!0,height:340,data:s,radius:.7,innerRadius:.65,angleField:"count",colorField:"type",color:["#21CCFF","#313CA9","#249EFF"],interactions:[{type:"element-single-selected"}],tooltip:{showMarkers:!1},label:{visible:!0,type:"spider",formatter:e=>`${(e.percent*100).toFixed(0)}%`,style:{fill:"#86909C",fontSize:14}},legend:{position:"bottom"},statistic:{title:{style:{fontSize:"14px",lineHeight:2,color:"rgb(--var(color-text-1))"},formatter:()=>"\u5185\u5BB9\u91CF"},content:{style:{fontSize:"16px",color:"rgb(--var(color-text-1))"},formatter:(e,c)=>{const d=c.reduce((p,u)=>p+u.count,0);return Number(d).toLocaleString()}}}})})]})}export{D as default};
