var yt=Object.defineProperty,Et=Object.defineProperties;var vt=Object.getOwnPropertyDescriptors;var V=Object.getOwnPropertySymbols;var Be=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable;var Ne=(e,n,o)=>n in e?yt(e,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[n]=o,_=(e,n)=>{for(var o in n||(n={}))Be.call(n,o)&&Ne(e,o,n[o]);if(V)for(var o of V(n))Ie.call(n,o)&&Ne(e,o,n[o]);return e},S=(e,n)=>Et(e,vt(n));var se=(e,n)=>{var o={};for(var s in e)Be.call(e,s)&&n.indexOf(s)<0&&(o[s]=e[s]);if(e!=null&&V)for(var s of V(e))n.indexOf(s)<0&&Ie.call(e,s)&&(o[s]=e[s]);return o};import{r as l,j as t,L as re,R as wt,a as c,B as R,A as ae,S as Re,T as W,b as Ft,c as Le,d as Ct,g as xt,I as Dt,e as St,f as Pe,h as kt,i as K,k as Me,l as H,m as ue,u as j,n as ie,o as At,p as Bt,D as Te,q as je,s as It,t as Nt,F as le,v as Rt,w as J,x as Lt,y as Pt,z as Mt,M as ce,C as L,E as Tt,G as jt,H as zt,J as Ot,K as $t,N as qt,O as Ut,P as Vt,Q as P,U as ze,V as Wt,W as Kt,X as Ht,Y as Jt,Z as Oe,_ as $e,$ as z,a0 as Gt,a1 as Xt,a2 as qe,a3 as Yt,a4 as Ue,a5 as de,a6 as Ve,a7 as Zt,a8 as Qt,a9 as en,aa as I,ab as tn,ac as nn,ad as on,ae as sn,af as rn,ag as We,ah as an}from"./vendor.1faad458.js";const un=function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))s(u);new MutationObserver(u=>{for(const a of u)if(a.type==="childList")for(const r of a.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&s(r)}).observe(document,{childList:!0,subtree:!0});function o(u){const a={};return u.integrity&&(a.integrity=u.integrity),u.referrerpolicy&&(a.referrerPolicy=u.referrerpolicy),u.crossorigin==="use-credentials"?a.credentials="include":u.crossorigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function s(u){if(u.ep)return;u.ep=!0;const a=o(u);fetch(u.href,a)}};un();const ln=!1,cn=!0,dn=!0,gn=!1,mn="#165DFF",pn=220;var fn={colorWeek:ln,navbar:cn,menu:dn,footer:gn,themeColor:mn,menuWidth:pn};const Ke={settings:fn,userInfo:{permissions:{}}};function hn(e=Ke,n){switch(n.type){case"update-settings":{const{settings:o}=n.payload;return S(_({},e),{settings:o})}case"update-userInfo":{const{userInfo:o=Ke.userInfo,userLoading:s}=n.payload;return S(_({},e),{userLoading:s,userInfo:o})}default:return e}}const bn="modulepreload",He={},_n="/",w=function(n,o){return!o||o.length===0?n():Promise.all(o.map(s=>{if(s=`${_n}${s}`,s in He)return;He[s]=!0;const u=s.endsWith(".css"),a=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${a}`))return;const r=document.createElement("link");if(r.rel=u?"stylesheet":bn,u||(r.as="script",r.crossOrigin=""),r.href=s,document.head.appendChild(r),u)return new Promise((i,d)=>{r.addEventListener("load",i),r.addEventListener("error",d)})})).then(()=>n())},ge=l.exports.createContext({}),Je={"en-US":{"menu.dashboard":"Dashboard","menu.dashboard.workplace":"Workplace","menu.user.info":"User Info","menu.user.setting":"User Setting","menu.user.switchRoles":"Switch Roles","menu.user.role.admin":"Admin","menu.user.role.user":"General User","menu.invoice":"Invoice Management","menu.invoice.list":"Invoice List","menu.invoice.exception":"Exception Records","menu.user":"User Management","menu.user.list":"User List","navbar.logout":"Logout","settings.title":"Settings","settings.themeColor":"Theme Color","settings.content":"Content Setting","settings.navbar":"Navbar","settings.menuWidth":"Menu Width (px)","settings.navbar.theme.toLight":"Click to use light mode","settings.navbar.theme.toDark":"Click to use dark mode","settings.menu":"Menu","settings.footer":"Footer","settings.otherSettings":"Other Settings","settings.colorWeek":"Color Week","settings.alertContent":'After the configuration is only temporarily effective, if you want to really affect the project, click the "Copy Settings" button below and replace the configuration in settings.json.',"settings.copySettings":"Copy Settings","settings.copySettings.message":"Copy succeeded, please paste to file src/settings.json.","settings.close":"Close","settings.color.tooltip":"10 gradient colors generated according to the theme color","message.tab.title.message":"Message","message.tab.title.notice":"Notice","message.tab.title.todo":"ToDo","message.allRead":"All Read","message.seeMore":"SeeMore","message.empty":"Empty","message.empty.tips":"No Content","message.lang.tips":"Language switch to ","navbar.search.placeholder":"Please search"},"zh-CN":{"menu.dashboard":"\u4EEA\u8868\u76D8","menu.dashboard.workplace":"\u5DE5\u4F5C\u53F0","menu.user.info":"\u7528\u6237\u4FE1\u606F","menu.user.setting":"\u7528\u6237\u8BBE\u7F6E","menu.user.switchRoles":"\u5207\u6362\u89D2\u8272","menu.user.role.admin":"\u7BA1\u7406\u5458","menu.user.role.user":"\u666E\u901A\u7528\u6237","menu.invoice":"\u53D1\u7968\u7BA1\u7406","menu.invoice.list":"\u53D1\u7968\u5217\u8868","menu.invoice.exception":"\u5F02\u5E38\u5355\u636E","menu.user":"\u7528\u6237\u7BA1\u7406","menu.user.list":"\u7528\u6237\u5217\u8868","navbar.logout":"\u9000\u51FA\u767B\u5F55","settings.title":"\u9875\u9762\u914D\u7F6E","settings.themeColor":"\u4E3B\u9898\u8272","settings.content":"\u5185\u5BB9\u533A\u57DF","settings.navbar":"\u5BFC\u822A\u680F","settings.menuWidth":"\u83DC\u5355\u5BBD\u5EA6 (px)","settings.navbar.theme.toLight":"\u70B9\u51FB\u5207\u6362\u4E3A\u4EAE\u8272\u6A21\u5F0F","settings.navbar.theme.toDark":"\u70B9\u51FB\u5207\u6362\u4E3A\u6697\u9ED1\u6A21\u5F0F","settings.menu":"\u83DC\u5355\u680F","settings.footer":"\u5E95\u90E8","settings.otherSettings":"\u5176\u4ED6\u8BBE\u7F6E","settings.colorWeek":"\u8272\u5F31\u6A21\u5F0F","settings.alertContent":'\u914D\u7F6E\u4E4B\u540E\u4EC5\u662F\u4E34\u65F6\u751F\u6548\uFF0C\u8981\u60F3\u771F\u6B63\u4F5C\u7528\u4E8E\u9879\u76EE\uFF0C\u70B9\u51FB\u4E0B\u65B9\u7684 "\u590D\u5236\u914D\u7F6E" \u6309\u94AE\uFF0C\u5C06\u914D\u7F6E\u66FF\u6362\u5230 settings.json \u4E2D\u5373\u53EF\u3002',"settings.copySettings":"\u590D\u5236\u914D\u7F6E","settings.copySettings.message":"\u590D\u5236\u6210\u529F\uFF0C\u8BF7\u7C98\u8D34\u5230 src/settings.json \u6587\u4EF6\u4E2D","settings.close":"\u5173\u95ED","settings.color.tooltip":"\u6839\u636E\u4E3B\u9898\u989C\u8272\u751F\u6210\u7684 10 \u4E2A\u68AF\u5EA6\u8272\uFF08\u5C06\u914D\u7F6E\u590D\u5236\u5230\u9879\u76EE\u4E2D\uFF0C\u4E3B\u9898\u8272\u624D\u80FD\u5BF9\u4EAE\u8272 / \u6697\u9ED1\u6A21\u5F0F\u540C\u65F6\u751F\u6548\uFF09","message.tab.title.message":"\u6D88\u606F","message.tab.title.notice":"\u901A\u77E5","message.tab.title.todo":"\u5F85\u529E","message.allRead":"\u5168\u90E8\u5DF2\u8BFB","message.seeMore":"\u67E5\u770B\u66F4\u591A","message.empty":"\u6E05\u7A7A","message.empty.tips":"\u6682\u65E0\u5185\u5BB9","message.lang.tips":"\u8BED\u8A00\u5207\u6362\u81F3 ","navbar.search.placeholder":"\u8F93\u5165\u5185\u5BB9\u67E5\u8BE2"}};function B(e=null){const{lang:n}=l.exports.useContext(ge);return(e||Je)[n]||{}}const Ge=e=>l.exports.createElement("svg",_({width:33,height:33,viewBox:"0 0 33 33",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),l.exports.createElement("g",{clipPath:"url(#clip0)"},l.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.37754 16.9795L12.7498 9.43027C14.7163 7.41663 17.9428 7.37837 19.9564 9.34482C19.9852 9.37297 20.0137 9.40145 20.0418 9.43027L20.1221 9.51243C22.1049 11.5429 22.1049 14.7847 20.1221 16.8152L12.7498 24.3644C10.7834 26.378 7.55686 26.4163 5.54322 24.4498C5.5144 24.4217 5.48592 24.3932 5.45777 24.3644L5.37754 24.2822C3.39468 22.2518 3.39468 19.0099 5.37754 16.9795Z",fill:"#12D2AC"}),l.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.0479 9.43034L27.3399 16.8974C29.3674 18.9735 29.3674 22.2883 27.3399 24.3644C25.3735 26.3781 22.147 26.4163 20.1333 24.4499C20.1045 24.4217 20.076 24.3933 20.0479 24.3644L12.7558 16.8974C10.7284 14.8213 10.7284 11.5065 12.7558 9.43034C14.7223 7.4167 17.9488 7.37844 19.9624 9.34489C19.9912 9.37304 20.0197 9.40152 20.0479 9.43034Z",fill:"#307AF2"}),l.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.1321 9.52163L23.6851 13.1599L16.3931 20.627L9.10103 13.1599L12.6541 9.52163C14.6707 7.45664 17.9794 7.4174 20.0444 9.434C20.074 9.46286 20.1032 9.49207 20.1321 9.52163Z",fill:"#0057FE"})),l.exports.createElement("defs",null,l.exports.createElement("clipPath",{id:"clip0"},l.exports.createElement("rect",{width:26,height:19,fill:"white",transform:"translate(3.5 7)"})))),yn="_footer_8a7h1_26";var O={"message-box":"_message-box_8a7h1_1","message-title":"_message-title_8a7h1_22",footer:yn,"footer-item":"_footer-item_8a7h1_29"};function En(e){const n=B(),{data:o,unReadData:s}=e;function u(r,i){r.status||e.onItemClick&&e.onItemClick(r,i)}function a(){e.onAllBtnClick&&e.onAllBtnClick(s,o)}return t(re,{noDataElement:t(wt,{status:"404",subTitle:n["message.empty.tips"]}),footer:c("div",{className:O.footer,children:[t("div",{className:O["footer-item"],children:t(R,{type:"text",size:"small",onClick:a,children:n["message.allRead"]})}),t("div",{className:O["footer-item"],children:t(R,{type:"text",size:"small",children:n["message.seeMore"]})})]}),children:o.map((r,i)=>t(re.Item,{actionLayout:"vertical",style:{opacity:r.status?.5:1},children:t("div",{style:{cursor:"pointer"},onClick:()=>{u(r,i)},children:t(re.Item.Meta,{avatar:r.avatar&&t(ae,{shape:"circle",size:36,children:t("img",{src:r.avatar})}),title:c("div",{className:O["message-title"],children:[c(Re,{size:4,children:[t("span",{children:r.title}),t(W.Text,{type:"secondary",children:r.subTitle})]}),r.tag&&r.tag.text?t(Ft,{color:r.tag.color,children:r.tag.text}):null]}),description:c("div",{children:[t(W.Paragraph,{style:{marginBottom:0},ellipsis:!0,children:r.content}),t(W.Text,{type:"secondary",style:{fontSize:12},children:r.time})]})})})},r.id))})}function vn(){const e=B(),[n,o]=l.exports.useState(!1),[s,u]=l.exports.useState({}),[a,r]=l.exports.useState([]);function i(g=!0){g&&o(!0),H.get("/api/message/list").then(f=>{r(f.data)}).finally(()=>{g&&o(!1)})}function d(g){const f=g.map(k=>k.id);H.post("/api/message/read",{ids:f}).then(()=>{i()})}l.exports.useEffect(()=>{i()},[]),l.exports.useEffect(()=>{const g=xt(a,"type");u(g)},[a]);const p=[{key:"message",title:e["message.tab.title.message"],titleIcon:t(Dt,{})},{key:"notice",title:e["message.tab.title.notice"],titleIcon:t(St,{})},{key:"todo",title:e["message.tab.title.todo"],titleIcon:t(Pe,{}),avatar:t(ae,{style:{backgroundColor:"#0FC6C2"},children:t(kt,{})})}];return t("div",{className:O["message-box"],children:t(K,{loading:n,style:{display:"block"},children:t(Me,{overflow:"dropdown",type:"rounded",defaultActiveTab:"message",destroyOnHide:!0,extra:t(R,{type:"text",onClick:()=>r([]),children:e["message.empty"]}),children:p.map(g=>{const{key:f,title:k,avatar:A}=g,E=s[f]||[],b=E.filter(v=>!v.status);return t(Me.TabPane,{title:c("span",{children:[k,b.length?`(${b.length})`:""]}),children:t(En,{data:E,unReadData:b,onItemClick:v=>{d([v])},onAllBtnClick:v=>{d(v)}})},f)})})})})}function wn({children:e}){return t(Le,{trigger:"hover",popup:()=>t(vn,{}),position:"br",unmountOnExit:!1,popupAlign:{bottom:4},children:t(Ct,{count:9,dot:!0,children:e})})}var Fn={"icon-button":"_icon-button_12azl_1"};function Cn(e,n){const a=e,{icon:o,className:s}=a,u=se(a,["icon","className"]);return t(R,_({ref:n,icon:o,shape:"circle",type:"secondary",className:ue(Fn["icon-button"],s)},u))}var G=l.exports.forwardRef(Cn);const xn="_block_byc7u_1",Dn="_title_byc7u_4";var me={block:xn,title:Dn,"switch-wrapper":"_switch-wrapper_byc7u_9"};function pe(e){const{title:n,options:o,children:s}=e,u=B(),a=j(i=>i.settings),r=ie();return c("div",{className:me.block,children:[t("h5",{className:me.title,children:n}),o&&o.map(i=>{const d=i.type||"switch";return c("div",{className:me["switch-wrapper"],children:[t("span",{children:u[i.name]}),d==="switch"&&t(At,{size:"small",checked:!!a[i.value],onChange:p=>{const g=S(_({},a),{[i.value]:p});r({type:"update-settings",payload:{settings:g}}),p&&i.value==="colorWeek"&&(document.body.style.filter="invert(80%)"),!p&&i.value==="colorWeek"&&(document.body.style.filter="none")}}),d==="number"&&t(Bt,{style:{width:80},size:"small",value:a.menuWidth,onChange:p=>{const g=S(_({},a),{[i.value]:p});r({type:"update-settings",payload:{settings:g}})}})]},i.value)}),s,t(Te,{})]})}const Sn="_input_77wyg_1",kn="_color_77wyg_9",An="_ul_77wyg_14",Bn="_li_77wyg_19";var X={input:Sn,color:kn,ul:An,li:Bn};function In(){const e=document.querySelector("body").getAttribute("arco-theme")||"light",n=j(r=>r.settings),o=B(),s=n.themeColor,u=je(s,{list:!0}),a=ie();return c("div",{children:[t(Le,{trigger:"hover",position:"bl",popup:()=>t(It,{color:s,onChangeComplete:r=>{const i=r.hex;a({type:"update-settings",payload:{settings:S(_({},n),{themeColor:i})}}),je(i,{list:!0,dark:e==="dark"}).forEach((p,g)=>{const f=Nt(p);document.body.style.setProperty(`--arcoblue-${g+1}`,f)})}}),children:c("div",{className:X.input,children:[t("div",{className:X.color,style:{backgroundColor:s}}),t("span",{children:s})]})}),t("ul",{className:X.ul,children:u.map((r,i)=>t("li",{className:X.li,style:{backgroundColor:r}},i))}),t(W.Paragraph,{style:{fontSize:12},children:o["settings.color.tooltip"]})]})}function Nn(e){const{trigger:n}=e,[o,s]=l.exports.useState(!1),u=B(),a=j(i=>i.settings);function r(){Mt(JSON.stringify(a,null,2)),ce.success(u["settings.copySettings.message"])}return c(le,{children:[n?Rt.cloneElement(n,{onClick:()=>s(!0)}):t(G,{icon:t(J,{}),onClick:()=>s(!0)}),c(Lt,{width:300,title:c(le,{children:[t(J,{}),u["settings.title"]]}),visible:o,okText:u["settings.copySettings"],cancelText:u["settings.close"],onOk:r,onCancel:()=>s(!1),children:[t(pe,{title:u["settings.themeColor"],children:t(In,{})}),t(pe,{title:u["settings.content"],options:[{name:"settings.navbar",value:"navbar"},{name:"settings.menu",value:"menu"},{name:"settings.footer",value:"footer"},{name:"settings.menuWidth",value:"menuWidth",type:"number"}]}),t(pe,{title:u["settings.otherSettings"],options:[{name:"settings.colorWeek",value:"colorWeek"}]}),t(Pt,{content:u["settings.alertContent"]})]})]})}const Rn="_navbar_1huq7_1",Ln="_left_1huq7_9",Pn="_logo_1huq7_13",Mn="_right_1huq7_27",Tn="_username_1huq7_41",jn="_round_1huq7_44";var N={navbar:Rn,left:Ln,logo:Pn,"logo-name":"_logo-name_1huq7_20",right:Mn,username:Tn,round:jn,"dropdown-icon":"_dropdown-icon_1huq7_50","fixed-settings":"_fixed-settings_1huq7_55"};function zn(e){return Object.prototype.toString.call(e)==="[object Array]"}const M=function(){try{return!(typeof window!="undefined"&&document!==void 0)}catch{return!0}}(),On=e=>{if(!M)return localStorage.getItem(e)};function $(e,n){const[o,s]=l.exports.useState(On(e)||n),u=r=>{M||(localStorage.setItem(e,r),r!==o&&s(r))},a=()=>{M||localStorage.removeItem(e)};return l.exports.useEffect(()=>{const r=localStorage.getItem(e);r&&s(r)},[]),[o,u,a]}const Xe=(e,n)=>!n||!n.length?!1:n.join("")==="*"?!0:e.every(o=>n.includes(o)),$n=(e,n)=>{const{resource:o,actions:s=[]}=e;if(o instanceof RegExp){const r=Object.keys(n).filter(i=>i.match(o));return r.length?r.every(i=>{const d=n[i];return Xe(s,d)}):!1}const u=n[o];return Xe(s,u)};var qn=(e,n)=>{const{requiredPermissions:o,oneOfPerm:s}=e;if(Array.isArray(o)&&o.length){let u=0;for(const a of o)$n(a,n)&&u++;return s?u>0:u===o.length}return!0};const fe=[{name:"menu.dashboard.workplace",key:"dashboard/workplace"},{name:"menu.invoice",key:"invoice",children:[{name:"menu.invoice.list",key:"invoice/list"},{name:"menu.invoice.exception",key:"invoice/exception"}]},{name:"menu.user",key:"user",children:[{name:"menu.user.list",key:"user/list"}]}],Ye=e=>{const n=e==="admin"?["*"]:["read"],o={};return fe.forEach(s=>{s.children&&s.children.forEach(u=>{o[u.name]=n})}),o},Un=e=>{const n=(a,r=[])=>{if(!a.length)return[];for(const i of a){const{requiredPermissions:d,oneOfPerm:p}=i;let g=!0;if(d&&(g=qn({requiredPermissions:d,oneOfPerm:p},e)),!!g)if(i.children&&i.children.length){const f=S(_({},i),{children:[]});n(i.children,f.children),f.children.length&&r.push(f)}else r.push(_({},i))}return r},[o,s]=l.exports.useState(fe);l.exports.useEffect(()=>{const a=n(fe);s(a)},[JSON.stringify(e)]);const u=l.exports.useMemo(()=>{var r,i;const a=o[0];return a?((i=(r=a==null?void 0:a.children)==null?void 0:r[0])==null?void 0:i.key)||a.key:""},[o]);return[o,u]},Vn="",x=H.create({baseURL:`${Vn}/admin`,timeout:1e4,headers:{"Content-Type":"application/json"}}),he="access_token",be="refresh_token";let _e=!1,ye=[];const Ze=(e,n=null)=>{ye.forEach(({resolve:o,reject:s})=>{e?s(e):o(n)}),ye=[]},Wn=()=>localStorage.getItem(he),Kn=()=>localStorage.getItem(be),Qe=(e,n)=>{localStorage.setItem(he,e),n&&localStorage.setItem(be,n)},Y=()=>{localStorage.removeItem(he),localStorage.removeItem(be),localStorage.removeItem("userStatus")},Hn=async()=>{const e=Kn();if(!e)throw new Error("No refresh token available");try{const n=await H.post("/api/auth/refresh",{refreshToken:e}),{access_token:o,refresh_token:s}=n.data.data;return Qe(o,s),o}catch(n){throw Y(),window.location.href="/login",n}};x.interceptors.request.use(e=>{const n=Wn();return n&&(e.headers.Authorization=`Bearer ${n}`),e},e=>Promise.reject(e));x.interceptors.response.use(e=>{const{data:n}=e;if(n&&n.code===-100){console.warn("Session expired:",n.msg),Y(),window.location.href="/login";const o=new Error(n.msg||"\u8EAB\u4EFD\u9A8C\u8BC1\u4F1A\u8BDD\u5DF2\u8FC7\u671F");return o.code=-1,o.isSessionExpired=!0,Promise.reject(o)}return e},async e=>{var s,u,a;const n=e.config;if(((s=e.response)==null?void 0:s.status)===401&&!n._retry){if(_e)return new Promise((r,i)=>{ye.push({resolve:r,reject:i})}).then(r=>(n.headers.Authorization=`Bearer ${r}`,x(n))).catch(r=>Promise.reject(r));n._retry=!0,_e=!0;try{const r=await Hn();return Ze(null,r),n.headers.Authorization=`Bearer ${r}`,x(n)}catch(r){return Ze(r,null),Y(),window.location.href="/login",Promise.reject(r)}finally{_e=!1}}const o=((a=(u=e.response)==null?void 0:u.data)==null?void 0:a.message)||e.message||"\u8BF7\u6C42\u5931\u8D25";return console.error("Request Error:",o),Promise.reject(e)});const Ee={get:(e,n={},o={})=>x.get(e,_({params:n},o)),post:(e,n={},o={})=>x.post(e,n,o),put:(e,n={},o={})=>x.put(e,n,o),delete:(e,n={})=>x.delete(e,n),patch:(e,n={},o={})=>x.patch(e,n,o),upload:(e,n,o={})=>x.post(e,n,_({headers:{"Content-Type":"multipart/form-data"}},o)),download:(e,n={},o={})=>x.get(e,_({params:n,responseType:"blob"},o))},Jn=async(e,n)=>{var o,s;try{const u=await Ee.post("/user/login",{username:e,password:n}),{code:a,msg:r,data:i}=u.data;return a===0?(i&&i.access_token&&Qe(i.access_token,i.refresh_token),localStorage.setItem("userStatus","login"),{success:!0,data:i}):{success:!1,message:r}}catch(u){return console.error("Login failed:",u),{success:!1,message:((s=(o=u.response)==null?void 0:o.data)==null?void 0:s.message)||"\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"}}},Gn=async()=>{try{await Ee.post("/user/logout")}catch(e){console.error("Logout API failed:",e)}finally{Y(),window.location.href="/login"}},Xn=async()=>{try{return(await Ee.get("/user/info")).data}catch(e){throw console.error("Get user info failed:",e),e}},Yn=()=>localStorage.getItem("userStatus")==="login";function Zn({show:e}){var b;const n=B(),o=j(v=>v.userInfo),s=ie(),[u,a]=$("userStatus"),[r,i]=$("userRole","admin"),{setLang:d,lang:p,theme:g,setTheme:f}=l.exports.useContext(ge);function k(){Gn(),a("logout"),window.location.href="/login"}function A(v){v==="logout"?k():ce.info(`You clicked ${v}`)}if(l.exports.useEffect(()=>{s({type:"update-userInfo",payload:{userInfo:S(_({},o),{permissions:Ye(r)})}})},[r]),!e)return t("div",{className:N["fixed-settings"],children:t(Nn,{trigger:t(R,{icon:t(J,{}),type:"primary",size:"large"})})});const E=c(L,{onClickMenuItem:A,children:[c(L.Item,{children:[t(J,{className:N["dropdown-icon"]}),n["menu.user.setting"]]},"setting"),t(Te,{style:{margin:"4px 0"}}),c(L.Item,{children:[t(Tt,{className:N["dropdown-icon"]}),n["navbar.logout"]]},"logout")]});return c("div",{className:N.navbar,children:[t("div",{className:N.left,children:c("div",{className:N.logo,children:[t(Ge,{}),t("div",{className:N["logo-name"],children:"\u7535\u5B50\u53D1\u7968\u5E73\u53F0"})]})}),c("ul",{className:N.right,children:[t("li",{children:t(jt,{triggerElement:t(G,{icon:t(zt,{})}),options:[{label:"\u4E2D\u6587",value:"zh-CN"},{label:"English",value:"en-US"}],value:p,triggerProps:{autoAlignPopupWidth:!1,autoAlignPopupMinWidth:!0,position:"br"},trigger:"hover",onChange:v=>{d(v);const Q=Je[v];ce.info(`${Q["message.lang.tips"]}${v}`)}})}),t("li",{children:t(wn,{children:t(G,{icon:t(Ot,{})})})}),t("li",{children:t($t,{content:g==="light"?n["settings.navbar.theme.toDark"]:n["settings.navbar.theme.toLight"],children:t(G,{icon:g!=="dark"?t(qt,{}):t(Ut,{}),onClick:()=>f(g==="light"?"dark":"light")})})}),o&&t("li",{children:t(Vt,{droplist:E,position:"br",children:t(ae,{size:32,style:{cursor:"pointer"},children:o.avatar?t("img",{src:o.avatar,alt:"avatar"}):(b=o.name)==null?void 0:b.charAt(0)})})})]})]})}const Qn="_footer_1si67_1";var eo={footer:Qn};function to(e={}){const s=e,{className:n}=s,o=se(s,["className"]);return t(P.Footer,S(_({className:ue(eo.footer,n)},o),{children:"Arco Design Pro"}))}function no(){const e=ze.parseUrl(M?"":window.location.href).query,n={};return Object.keys(e).forEach(o=>{e[o]==="true"&&(n[o]=!0),e[o]==="false"&&(n[o]=!1)}),n}const oo="_layout_1mw86_1",so="_icon_1mw86_87",ro="_spin_1mw86_109";var F={layout:oo,"layout-navbar":"_layout-navbar_1mw86_5","layout-navbar-hidden":"_layout-navbar-hidden_1mw86_14","layout-sider":"_layout-sider_1mw86_17","collapse-btn":"_collapse-btn_1mw86_51","menu-wrapper":"_menu-wrapper_1mw86_68",icon:so,"icon-empty":"_icon-empty_1mw86_91","layout-content":"_layout-content_1mw86_96","layout-content-wrapper":"_layout-content-wrapper_1mw86_103","layout-breadcrumb":"_layout-breadcrumb_1mw86_106",spin:ro};function ao(e,n){const o=Wt(e,n);return o.preload=e.requireAsync||e,o}function uo(e){return e.error?(console.error(e.error),null):t("div",{className:F.spin,children:t(K,{})})}var et=e=>ao(e,{fallback:uo({pastDelay:!0,error:!1,timedOut:!1})});const io=L.Item,lo=L.SubMenu,co=P.Sider,go=P.Content;function mo(e){switch(e){case"dashboard":case"dashboard/workplace":return t(Yt,{className:F.icon});case"invoice":return t(Pe,{className:F.icon});case"user":return t(qe,{className:F.icon});case"example":return t(Xt,{className:F.icon});default:return t("div",{className:F["icon-empty"]})}}function po(e){const n={"./pages/login/banner.tsx":()=>w(()=>Promise.resolve().then(function(){return Fo}),void 0),"./pages/login/form.tsx":()=>w(()=>Promise.resolve().then(function(){return wo}),void 0),"./pages/login/index.tsx":()=>w(()=>Promise.resolve().then(function(){return Co}),void 0),"./pages/dashboard/workplace/announcement.tsx":()=>w(()=>import("./announcement.480a4cae.js"),["assets/announcement.480a4cae.js","assets/announcement.4446c828.css","assets/index.4623c961.css","assets/index.e7a6af1d.css","assets/index.43714dcb.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css","assets/index.9464998a.js"]),"./pages/dashboard/workplace/carousel.tsx":()=>w(()=>import("./carousel.e5aad4d2.js"),["assets/carousel.e5aad4d2.js","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css"]),"./pages/dashboard/workplace/content-percentage.tsx":()=>w(()=>import("./content-percentage.f35dd7e9.js"),["assets/content-percentage.f35dd7e9.js","assets/index.e7a6af1d.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css","assets/index.6dde18eb.js","assets/index.9464998a.js"]),"./pages/dashboard/workplace/docs.tsx":()=>w(()=>import("./docs.deff4f73.js"),["assets/docs.deff4f73.js","assets/docs.e521c9d6.css","assets/index.e7a6af1d.css","assets/index.43714dcb.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css","assets/index.9464998a.js"]),"./pages/dashboard/workplace/index.tsx":()=>w(()=>import("./index.ba304042.js"),["assets/index.ba304042.js","assets/index.8c5bf3cd.css","assets/index.43714dcb.css","assets/index.4623c961.css","assets/index.e7a6af1d.css","assets/index.ef62d849.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css","assets/overview.d275a5dd.js","assets/overview.65964fa4.css","assets/index.6dde18eb.js","assets/index.9464998a.js","assets/popular-contents.762a25a7.js","assets/popular-contents.5d7b60b5.css","assets/content-percentage.f35dd7e9.js","assets/shortcuts.dc91682c.js","assets/shortcuts.0626e3d2.css","assets/announcement.480a4cae.js","assets/announcement.4446c828.css","assets/carousel.e5aad4d2.js","assets/docs.deff4f73.js","assets/docs.e521c9d6.css"]),"./pages/dashboard/workplace/overview.tsx":()=>w(()=>import("./overview.d275a5dd.js"),["assets/overview.d275a5dd.js","assets/overview.65964fa4.css","assets/index.43714dcb.css","assets/index.4623c961.css","assets/index.e7a6af1d.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css","assets/index.6dde18eb.js","assets/index.9464998a.js"]),"./pages/dashboard/workplace/popular-contents.tsx":()=>w(()=>import("./popular-contents.762a25a7.js"),["assets/popular-contents.762a25a7.js","assets/popular-contents.5d7b60b5.css","assets/index.ef62d849.css","assets/index.e7a6af1d.css","assets/index.43714dcb.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css","assets/index.9464998a.js"]),"./pages/dashboard/workplace/shortcuts.tsx":()=>w(()=>import("./shortcuts.dc91682c.js"),["assets/shortcuts.dc91682c.js","assets/shortcuts.0626e3d2.css","assets/index.e7a6af1d.css","assets/index.43714dcb.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css","assets/index.9464998a.js"]),"./pages/exception/403/index.tsx":()=>w(()=>import("./index.09101c29.js"),["assets/index.09101c29.js","assets/index.6ecaddee.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css"]),"./pages/invoice/exception/index.tsx":()=>w(()=>import("./index.b13ab0ca.js"),["assets/index.b13ab0ca.js","assets/index.ef62d849.css","assets/index.e7a6af1d.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css"]),"./pages/invoice/list/index.tsx":()=>w(()=>import("./index.e721bc92.js"),["assets/index.e721bc92.js","assets/index.cdc3ab8e.css","assets/index.87aa3815.css","assets/index.43714dcb.css","assets/index.ef62d849.css","assets/index.e7a6af1d.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css"]),"./pages/user/list/index.tsx":()=>w(()=>import("./index.31163967.js"),["assets/index.31163967.js","assets/index.c382363e.css","assets/index.43714dcb.css","assets/index.87aa3815.css","assets/index.ef62d849.css","assets/index.e7a6af1d.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css"])},o=[];function s(u){u.forEach(a=>{a.key&&!a.children?(a.component=et(n[`./pages/${a.key}/index.tsx`]),o.push(a)):zn(a.children)&&a.children.length&&s(a.children)})}return s(e),o}function fo(){const e=no(),n=Kt(),o=n.location.pathname,s=ze.parseUrl(o).url.slice(1),u=B(),{settings:a,userLoading:r,userInfo:i}=j(m=>m),[d,p]=Un(i==null?void 0:i.permissions),g=[s||p],f=(s||p).split("/"),k=f.slice(0,f.length-1),[A,E]=l.exports.useState([]),[b,v]=l.exports.useState(!1),[Q,at]=l.exports.useState(g),[ee,we]=l.exports.useState(k),q=l.exports.useRef(new Map),te=l.exports.useRef(new Map),ut=60,Fe=b?48:a.menuWidth,ne=a.navbar&&e.navbar!==!1,Ce=a.menu&&e.menu!==!1,it=a.footer&&e.footer!==!1,xe=l.exports.useMemo(()=>po(d)||[],[d]);function lt(m){const C=xe.find(D=>D.key===m),U=C.component.preload();Ue.start(),U.then(()=>{n.push(C.path?C.path:`/${m}`),Ue.done()})}function ct(){v(m=>!m)}const dt=Ce?{paddingLeft:Fe}:{},De=ne?{paddingTop:ut}:{},gt=_(_({},dt),De);function mt(m){return q.current.clear(),function C(T,U,D=[]){return T.map(h=>{const{breadcrumb:ft=!0,ignore:ht}=h,bt=mo(h.key),Se=c(le,{children:[bt," ",m[h.name]||h.name]});q.current.set(`/${h.key}`,ft?[...D,h.name]:[]);const ke=(h.children||[]).filter(oe=>{const{ignore:Ae,breadcrumb:_t=!0}=oe;return(Ae||h.ignore)&&q.current.set(`/${oe.key}`,_t?[...D,h.name,oe.name]:[]),!Ae});return ht?"":ke.length?(te.current.set(h.key,{subMenu:!0}),t(lo,{title:Se,children:C(ke,U+1,[...D,h.name])},h.key)):(te.current.set(h.key,{menuItem:!0}),t(io,{children:Se},h.key))})}}function pt(){const m=o.split("/"),C=[],T=[...ee];for(;m.length>0;){const D=m.join("/").replace(/^\//,""),h=te.current.get(D);h&&h.menuItem&&C.push(D),h&&h.subMenu&&!ee.includes(D)&&T.push(D),m.pop()}at(C),we(T)}return l.exports.useEffect(()=>{const m=q.current.get(o);E(m||[]),pt()},[o]),c(P,{className:F.layout,children:[t("div",{className:ue(F["layout-navbar"],{[F["layout-navbar-hidden"]]:!ne}),children:t(Zn,{show:ne})}),r?t(K,{className:F.spin}):c(P,{children:[Ce&&c(co,{className:F["layout-sider"],width:Fe,collapsed:b,onCollapse:v,trigger:null,collapsible:!0,breakpoint:"xl",style:De,children:[t("div",{className:F["menu-wrapper"],children:t(L,{collapse:b,onClickMenuItem:lt,selectedKeys:Q,openKeys:ee,onClickSubMenu:(m,C)=>{we(C)},children:mt(u)(d,1)})}),t("div",{className:F["collapse-btn"],onClick:ct,children:b?t(Ht,{}):t(Jt,{})})]}),c(P,{className:F["layout-content"],style:gt,children:[c("div",{className:F["layout-content-wrapper"],children:[!!A.length&&t("div",{className:F["layout-breadcrumb"],children:t(Oe,{children:A.map((m,C)=>t(Oe.Item,{children:typeof m=="string"&&u[m]||m},C))})}),t(go,{children:c($e,{children:[xe.map((m,C)=>t(z,{path:`/${m.key}`,component:m.component},C)),t(z,{exact:!0,path:"/",children:t(Gt,{to:`/${p}`})}),t(z,{path:"*",component:et(()=>w(()=>import("./index.09101c29.js"),["assets/index.09101c29.js","assets/index.6ecaddee.css","assets/vendor.1faad458.js","assets/vendor.b62ae5ca.css"]))})]})})]}),it&&t(to,{})]})]})]})}const tt={"en-US":{"login.form.title":"Login","login.form.userName.errMsg":"Username cannot be empty","login.form.password.errMsg":"Password cannot be empty","login.form.login.errMsg":"Login error, please refresh and try again","login.form.userName.placeholder":"Username: admin","login.form.password.placeholder":"Password: admin","login.form.rememberPassword":"Remember password","login.form.forgetPassword":"Forgot password","login.form.login":"login","login.form.register":"register account","login.banner.slogan1":"","login.banner.subSlogan1":"","login.banner.slogan2":"Built-in solutions to common problems","login.banner.subSlogan2":"Internationalization, routing configuration, state management everything","login.banner.slogan3":"Access visualization enhancement tool AUX","login.banner.subSlogan3":"Realize flexible block development"},"zh-CN":{"login.form.title":"\u767B\u5F55","login.form.subtitle":"\u4F7F\u7528\u8D26\u53F7\u5BC6\u7801\u767B\u5F55","login.form.userName.errMsg":"\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A","login.form.password.errMsg":"\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A","login.form.login.errMsg":"\u767B\u5F55\u51FA\u9519\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5","login.form.userName.placeholder":"\u7528\u6237\u540D\uFF1Aadmin","login.form.password.placeholder":"\u5BC6\u7801\uFF1Aadmin","login.form.rememberPassword":"\u8BB0\u4F4F\u5BC6\u7801","login.form.forgetPassword":"\u5FD8\u8BB0\u5BC6\u7801","login.form.login":"\u767B\u5F55","login.form.register":"\u6CE8\u518C\u8D26\u53F7","login.banner.slogan1":"","login.banner.subSlogan1":"","login.banner.slogan2":"\u5185\u7F6E\u4E86\u5E38\u89C1\u95EE\u9898\u7684\u89E3\u51B3\u65B9\u6848","login.banner.subSlogan2":"\u56FD\u9645\u5316\uFF0C\u8DEF\u7531\u914D\u7F6E\uFF0C\u72B6\u6001\u7BA1\u7406\u5E94\u6709\u5C3D\u6709","login.banner.slogan3":"\u63A5\u5165\u53EF\u89C6\u5316\u589E\u5F3A\u5DE5\u5177AUX","login.banner.subSlogan3":"\u5B9E\u73B0\u7075\u6D3B\u7684\u533A\u5757\u5F0F\u5F00\u53D1"}},ho="_container_13zaa_1",bo="_banner_13zaa_5",_o="_content_13zaa_9",yo="_footer_13zaa_14",Eo="_logo_13zaa_20",vo="_carousel_13zaa_48";var y={container:ho,banner:bo,content:_o,footer:yo,logo:Eo,"logo-text":"_logo-text_13zaa_28","banner-inner":"_banner-inner_13zaa_39",carousel:vo,"carousel-item":"_carousel-item_13zaa_51","carousel-title":"_carousel-title_13zaa_58","carousel-sub-title":"_carousel-sub-title_13zaa_64","carousel-image":"_carousel-image_13zaa_70","login-form-wrapper":"_login-form-wrapper_13zaa_74","login-form-title":"_login-form-title_13zaa_77","login-form-sub-title":"_login-form-sub-title_13zaa_83","login-form-error-msg":"_login-form-error-msg_13zaa_88","login-form-password-actions":"_login-form-password-actions_13zaa_93","login-form-register-btn":"_login-form-register-btn_13zaa_97"};function nt(){const e=l.exports.useRef(),[n,o]=l.exports.useState(""),[s,u]=l.exports.useState(!1),[a,r,i]=$("loginParams"),d=B(tt),[p,g]=l.exports.useState(!!a);function f(E){p?r(JSON.stringify(E)):i(),window.location.href="/"}async function k(E){o(""),u(!0);try{const b=await Jn(E.userName,E.password);b.success?f(E):o(b.message||d["login.form.login.errMsg"])}catch(b){o(b.message||d["login.form.login.errMsg"])}finally{u(!1)}}function A(){e.current.validate().then(E=>{k(E)})}return l.exports.useEffect(()=>{const E=!!a;if(g(E),e.current&&E){const b=JSON.parse(a);e.current.setFieldsValue(b)}},[a]),c("div",{className:y["login-form-wrapper"],children:[t("div",{className:y["login-form-title"],children:d["login.form.title"]}),t("div",{className:y["login-form-sub-title"],children:d["login.form.subtitle"]}),t("div",{className:y["login-form-error-msg"],children:n}),c(de,{className:y["login-form"],layout:"vertical",ref:e,initialValues:{userName:"",password:""},children:[t(de.Item,{field:"userName",rules:[{required:!0,message:d["login.form.userName.errMsg"]}],children:t(Ve,{prefix:t(qe,{}),placeholder:d["login.form.userName.placeholder"],onPressEnter:A})}),t(de.Item,{field:"password",rules:[{required:!0,message:d["login.form.password.errMsg"]}],children:t(Ve.Password,{prefix:t(Zt,{}),placeholder:d["login.form.password.placeholder"],onPressEnter:A})}),c(Re,{size:16,direction:"vertical",children:[t("div",{className:y["login-form-password-actions"],children:t(Qt,{checked:p,onChange:g,children:d["login.form.rememberPassword"]})}),t(R,{type:"primary",long:!0,onClick:A,loading:s,children:d["login.form.login"]})]})]})]})}var wo=Object.freeze({__proto__:null,[Symbol.toStringTag]:"Module",default:nt});function ot(){const e=B(tt),n=[{slogan:e["login.banner.slogan1"],subSlogan:e["login.banner.subSlogan1"],image:"http://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/6c85f43aed61e320ebec194e6a78d6d3.png~tplv-uwbnlip3yd-png.png"}];return t(en,{className:y.carousel,animation:"fade",children:n.map((o,s)=>t("div",{children:c("div",{className:y["carousel-item"],children:[t("div",{className:y["carousel-title"],children:o.slogan}),t("div",{className:y["carousel-sub-title"],children:o.subSlogan}),t("img",{alt:"banner-image",className:y["carousel-image"],src:o.image})]})},`${s}`))})}var Fo=Object.freeze({__proto__:null,[Symbol.toStringTag]:"Module",default:ot});function ve(){const[e,n]=l.exports.useState(!0);return l.exports.useEffect(()=>{if(document.body.setAttribute("arco-theme","light"),Yn()){window.location.href="/";return}n(!1)},[]),e?t("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",backgroundColor:"#f7f8fa"},children:t(K,{size:40})}):c("div",{className:y.container,children:[c("div",{className:y.logo,children:[t(Ge,{}),t("div",{className:y["logo-text"],children:"\u6D59\u4E8C\u773C\u79D1\u7535\u5B50\u53D1\u7968\u5E73\u53F0"})]}),t("div",{className:y.banner,children:t("div",{className:y["banner-inner"],children:t(ot,{})})}),t("div",{className:y.content,children:t("div",{className:y["content-inner"],children:t(nt,{})})})]})}ve.displayName="LoginPage";var Co=Object.freeze({__proto__:null,[Symbol.toStringTag]:"Module",default:ve});function xo(){return localStorage.getItem("userStatus")==="login"}function Do(e){e==="dark"?document.body.setAttribute("arco-theme","dark"):document.body.removeAttribute("arco-theme")}var st=e=>{const{mock:n=!1,setup:o}=e;n!==!1&&o()};M||(I.XHR.prototype.withCredentials=!0,st({setup:()=>{const e=window.localStorage.getItem("userRole")||"admin";I.mock(new RegExp("/api/user/userInfo"),()=>I.mock({name:"admin",avatar:"https://lf1-xgcdn-tos.pstatp.com/obj/vcloud/vadmin/start.8e0e4855ee346a46ccff8ff3e24db27b.png",email:"<EMAIL>",job:"frontend",jobName:"\u524D\u7AEF\u5F00\u53D1\u5DE5\u7A0B\u5E08",organization:"Frontend",organizationName:"\u524D\u7AEF",location:"beijing",locationName:"\u5317\u4EAC",introduction:"\u738B\u529B\u7FA4\u5E76\u975E\u662F\u4E00\u4E2A\u771F\u5B9E\u5B58\u5728\u7684\u4EBA\u3002",personalWebsite:"https://www.arco.design",verified:!0,phoneNumber:/177[*]{6}[0-9]{2}/,accountId:/[a-z]{4}[-][0-9]{8}/,registrationTime:I.Random.datetime("yyyy-MM-dd HH:mm:ss"),permissions:Ye(e)})),I.mock(new RegExp("/api/user/login"),n=>{const{userName:o,password:s}=JSON.parse(n.body);return o?s?o==="admin"&&s==="admin"?{status:"ok"}:{status:"error",msg:"\u8D26\u53F7\u6216\u8005\u5BC6\u7801\u9519\u8BEF"}:{status:"error",msg:"\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A"}:{status:"error",msg:"\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A"}})}}));const rt=[],So=()=>[{id:1,type:"message",title:"\u90D1\u66E6\u6708",subTitle:"\u7684\u79C1\u4FE1",avatar:"//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/8361eeb82904210b4f55fab888fe8416.png~tplv-uwbnlip3yd-webp.webp",content:"\u5BA1\u6279\u8BF7\u6C42\u5DF2\u53D1\u9001\uFF0C\u8BF7\u67E5\u6536",time:"\u4ECA\u5929 12:30:01"},{id:2,type:"message",title:"\u5B81\u6CE2",subTitle:"\u7684\u56DE\u590D",avatar:"//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp",content:"\u6B64\u5904 bug \u5DF2\u7ECF\u4FEE\u590D\uFF0C\u5982\u6709\u95EE\u9898\u8BF7\u67E5\u9605\u6587\u6863\u6216\u8005\u7EE7\u7EED github \u63D0 issue\uFF5E",time:"\u4ECA\u5929 12:30:01"},{id:3,type:"message",title:"\u5B81\u6CE2",subTitle:"\u7684\u56DE\u590D",avatar:"//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp",content:"\u6B64\u5904 bug \u5DF2\u7ECF\u4FEE\u590D",time:"\u4ECA\u5929 12:20:01"},{id:4,type:"todo",title:"\u57DF\u540D\u670D\u52A1",content:"\u5185\u5BB9\u8D28\u68C0\u961F\u5217\u4E8E 2021-12-01 19:50:23 \u8FDB\u884C\u53D8\u66F4\uFF0C\u8BF7\u91CD\u65B0",tag:{text:"\u672A\u5F00\u59CB",color:"gray"}},{id:5,type:"todo",title:"\u5185\u5BB9\u5BA1\u6279\u901A\u77E5",content:"\u5B81\u9759\u63D0\u4EA4\u4E8E 2021-11-05\uFF0C\u9700\u8981\u60A8\u5728 2011-11-07\u4E4B\u524D\u5BA1\u6279",tag:{text:"\u8FDB\u884C\u4E2D",color:"arcoblue"}},{id:6,type:"notice",title:"\u8D28\u68C0\u961F\u5217\u53D8\u66F4",content:"\u60A8\u7684\u4EA7\u54C1\u4F7F\u7528\u671F\u9650\u5373\u5C06\u622A\u6B62\uFF0C\u5982\u9700\u7EE7\u7EED\u4F7F\u7528\u4EA7\u54C1\u8BF7\u524D\u5F80\u8D2D\u2026",tag:{text:"\u5373\u5C06\u5230\u671F",color:"red"}},{id:7,type:"notice",title:"\u89C4\u5219\u5F00\u901A\u6210\u529F",subTitle:"",avatar:"",content:"\u5185\u5BB9\u5C4F\u853D\u89C4\u5219\u4E8E 2021-12-01 \u5F00\u901A\u6210\u529F\u5E76\u751F\u6548\u3002",tag:{text:"\u5DF2\u5F00\u901A",color:"green"}}].map(e=>S(_({},e),{status:rt.indexOf(e.id)===-1?0:1}));st({setup:()=>{I.mock(new RegExp("/api/message/list"),()=>So()),I.mock(new RegExp("/api/message/read"),e=>{const{ids:n}=JSON.parse(e.body);return rt.push(...n||[]),!0})}});M||I.setup({timeout:"500-1500"});const Z=tn(hn);function ko(){const[e,n]=$("arco-lang","zh-CN"),[o,s]=$("arco-theme","light");function u(){switch(e){case"zh-CN":return We;case"en-US":return an;default:return We}}function a(){Z.dispatch({type:"update-userInfo",payload:{userLoading:!0}}),Xn().then(i=>{Z.dispatch({type:"update-userInfo",payload:{userInfo:i.data,userLoading:!1}})}).catch(i=>{console.error("Failed to fetch user info:",i),Z.dispatch({type:"update-userInfo",payload:{userLoading:!1}}),(i.isSessionExpired||i.code===-1)&&(localStorage.removeItem("userStatus"),window.location.pathname.replace(/\//g,"")!=="login"&&(window.location.pathname="/login"))})}l.exports.useEffect(()=>{xo()?a():window.location.pathname.replace(/\//g,"")!=="login"&&(window.location.pathname="/login")},[]),l.exports.useEffect(()=>{Do(o)},[o]);const r={lang:e,setLang:n,theme:o,setTheme:s};return t(on,{children:t(sn,{locale:u(),componentConfig:{Card:{bordered:!1},List:{bordered:!1},Table:{border:!1}},children:t(rn,{store:Z,children:t(ge.Provider,{value:r,children:c($e,{children:[t(z,{path:"/login",component:ve}),t(z,{path:"/",component:fo})]})})})})})}nn.render(t(ko,{}),document.getElementById("root"));export{Ee as a,Vn as b,st as s,B as u};
