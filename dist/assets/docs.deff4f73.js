import{a as t,ai as c,j as e,T as o,aj as i}from"./vendor.1faad458.js";/* empty css               *//* empty css               */import{u as d}from"./index.9ffb96ac.js";import{i as l}from"./index.9464998a.js";const p="_docs_lmqvd_1",m="_link_lmqvd_5";var r={docs:p,link:m};const h={react:"https://arco.design/react/docs/start",vue:"https://arco.design/vue/docs/start",designLab:"https://arco.design/themes",materialMarket:"https://arco.design/material/"};function v(){const s=d(l);return t(c,{children:[t("div",{style:{display:"flex",justifyContent:"space-between"},children:[e(o.Title,{heading:6,children:s["workplace.docs"]}),e(i,{children:s["workplace.seeMore"]})]}),e("div",{className:r.docs,children:Object.entries(h).map(([a,n])=>e(i,{className:r.link,href:n,target:"_blank",children:s[`workplace.${a}`]},a))})]})}export{v as default};
